function initializeApp() {
    const paperTitleEl = document.getElementById('paper-title');
    const paperContentEl = document.getElementById('paper-content');

    if (!paperTitleEl || !paperContentEl) {
        setTimeout(initializeApp, 100);
        return;
    }

    let params = new URLSearchParams(window.location.search);
    let year = params.get('year');
    let lang = params.get('lang');

    if (!year || !lang) {
        const pathParts = window.location.pathname.split('/');
        // Expected path structure: /.../SUBJECT/YEAR/LANG/...
        // For example: /m1%20-%20latest_copy/2024/chi/2024_web/index.html
        // or /Core_latest%20copy/2023/ENG/2023_web/index.html
        
        // Find the index of a year-like part
        let yearIndex = -1;
        for (let i = pathParts.length - 1; i >= 0; i--) {
            if (/^\d{4}$/.test(pathParts[i])) {
                yearIndex = i;
                break;
            }
        }

        if (yearIndex !== -1) {
            year = pathParts[yearIndex];
            lang = pathParts[yearIndex + 1];
        }
    }
    
    // Determine subject from the path
    const path = window.location.pathname.toLowerCase();
    let subject = 'core'; // Default subject
    if (path.includes('m1')) {
        subject = 'm1';
    }

    let jsonPath;
    if (subject === 'core') {
        jsonPath = `../dse_core_${year}_paper_${lang.toLowerCase()}.json`;
    } else {
        jsonPath = `../dse_m1_${year}_paper.json`;
    }

    fetch(jsonPath)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            paperTitleEl.textContent = data.title;
            document.title = data.title;
            renderPaper(data, paperContentEl, lang);
            if (window.MathJax) {
                window.MathJax.typesetPromise([paperContentEl]);
            }
        })
        .catch(error => {
            console.error('Error loading paper data:', error);
            paperContentEl.innerHTML = `<p>Error loading paper content. Please check the file path and JSON format.</p><p>${error}</p>`;
        });
}

document.addEventListener('DOMContentLoaded', initializeApp);

function renderPaper(data, container, lang) {
    let htmlContent = '';
    const { sections, image_base_path } = data;

    const questionLabel = lang.toLowerCase().startsWith('chi') ? '第' : 'Question';
    const questionLabelEnd = lang.toLowerCase().startsWith('chi') ? '題' : '';
    const marksLabel = lang.toLowerCase().startsWith('chi') ? '分' : 'marks';
    const totalMarksLabel = lang.toLowerCase().startsWith('chi') ? '總分' : 'Total Marks';

    sections.forEach(section => {
        htmlContent += `<div class="section">`;
        htmlContent += `<h2 class="section-title">${section.title} (${totalMarksLabel}: ${section.total_marks})</h2>`;
        
        section.questions.forEach(q => {
            htmlContent += `<div class="question">`;
            htmlContent += `
                <div class="question-header">
                    <span class="question-id">${questionLabel} ${q.id}${questionLabelEnd}</span>
                    <span class="topic-info">${q.topic.code} - ${q.topic.name}</span>
                    <span>(${q.marks} ${marksLabel})</span>
                </div>
            `;
            htmlContent += `<div class="question-content">${(q.content || '').replace(/\n/g, '<br>').replace(/<table>/g, '<table class="data-table">')}</div>`;
            
            if (q.images && q.images.length > 0) {
                q.images.forEach(imgPath => {
                    const fullImagePath = `../${image_base_path}/images/${imgPath}`;
                    htmlContent += `<div class="question-image"><img src="${encodeURI(fullImagePath)}" alt="Question Image"></div>`;
                });
            }

            if (q.sub_questions && q.sub_questions.length > 0) {
                htmlContent += renderSubQuestions(q.sub_questions, 1, image_base_path, lang);
            }

            htmlContent += `</div>`;
        });

        htmlContent += `</div>`;
    });

    container.innerHTML = htmlContent;
}

function renderSubQuestions(subQuestions, depth, image_base_path, lang) {
    const marksLabel = lang.toLowerCase().startsWith('chi') ? '分' : 'marks';
    let html = '<div class="sub-questions-container">';
    (subQuestions || []).forEach(sq => {
        html += `<div class="sub-question depth-${depth}">`;
        html += `
            <div class="sub-question-header">
                <span class="sub-question-part">(${sq.id})</span>
                ${sq.marks ? `<span>(${sq.marks} ${marksLabel})</span>` : ''}
            </div>
        `;
        html += `<div class="sub-question-content">${(sq.content || '').replace(/\n/g, '<br>').replace(/<table>/g, '<table class="data-table">')}</div>`;

        if (sq.images && sq.images.length > 0) {
            sq.images.forEach(imgPath => {
                const fullImagePath = `../${image_base_path}/images/${imgPath}`;
                htmlContent += `<div class="question-image"><img src="${encodeURI(fullImagePath)}" alt="Sub-question Image"></div>`;
            });
        }

        if (sq.sub_questions && sq.sub_questions.length > 0) {
            html += renderSubQuestions(sq.sub_questions, depth + 1, image_base_path, lang);
        }
        html += `</div>`;
    });
    html += '</div>';
    return html;
}