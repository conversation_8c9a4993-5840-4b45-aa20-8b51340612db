# DSE M1 試卷 Markdown 轉 JSON 操作指南

## 📋 目的
本文檔為AI助手提供標準化的操作指南，確保將DSE M1試卷的Markdown文件正確轉換為結構化的JSON格式，同時確保數據能支持前端的各種功能需求（如階層式顯示）。

## 🏗️ JSON結構概覽

### 基本結構
```json
{
  "paper_info": { ... },
  "statistics": { ... },
  "sections": [ ... ],
  "images": [ ... ]
}
```

## 📝 詳細轉換規則

### 1. paper_info 部分
```json
{
  "paper_info": {
    "subject": "MATH_M1",
    "year": 2012,                    // 從文件名或內容中提取年份
    "language": "CHI",               // CHI=中文, ENG=英文
    "total_questions": 14,           // 統計總題數
    "total_sections": 2,             // 通常為甲部+乙部=2
    "paper_title": "數學延伸部分單元一",
    "paper_subtitle": "微積分與統計",
    "exam_duration": "2小時30分鐘"    // 如果有提及考試時間
  }
}
```

### 2. statistics 部分
```json
{
  "statistics": {
    "total_marks": 100,              // 計算所有題目分數總和
    "section_a_marks": 50,           // 甲部總分
    "section_b_marks": 50,           // 乙部總分
    "total_images": 1                // 統計圖片數量
  }
}
```

### 3. sections 部分
包含 `甲部` 和 `乙部` 的數組，結構如下：
```json
{
  "name": "甲部",
  "total_marks": 50,
  "questions": [ ... ]
}
```

### 4. 題目 (questions) 結構

#### 4.1 基本題目格式
```json
{
  "number": "1",                     // 題目編號（字符串）
  "marks": 4,                        // 題目總分（數字）
  "topic_code": "CAL-DER",           // 主題編碼（參考topic_codes.md）
  "topic_name": "導數",              // 主題名稱（中文）
  "content": "題目主要內容...",        // 題目描述
  "sub_questions": [ ... ],          // 子題目數組
  "images": [ ... ],                 // 圖片文件名數組
  "full_content": [ ... ]            // 額外內容（如表格）
}
```

#### 4.2 子題目 (sub_questions) 格式與階層式顯示
這是確保前端功能正確的關鍵部分。

```json
{
  "part": "a(i)",
  "marks": 2,
  "content": "子題內容..."
}
```

**⚠️ 重要規則：**
- **保留完整題號結構**: 為了讓前端能夠正確呈現階層關係（例如，(i) 是 (a) 的子題），`part` 欄位 **必須** 保留完整的父級題號，例如 `a(i)`、`c(ii)`。
- **禁止簡化**: **不要** 在生成JSON時將 `a(i)` 簡化為 `i`。這樣做會破壞數據的層級結構，導致前端無法實現階層式顯示。
- **前端負責顯示**: 前端腳本會自動解析 `part` 欄位，並只提取括號內的內容（如 `(i)`）用於顯示，從而保證視覺上的簡潔。數據的完整性是為了功能的正確性。

### 5. 特殊內容處理

#### 5.1 數學公式
- 保持原始LaTeX格式：`$x^2$`、`$$\int_0^1 f(x)dx$$`。
- 確保JSON字符串中的 `\` 被正確轉義（例如 `\\frac`）。

#### 5.2 表格 (full_content)
- 對於表格，將其轉換為 `full_content` 中的一個對象。
- `type` 應為 `"table"`。
- `content` 應為一個字符串，其中不同的 **列** 由 `;` 分隔，**標題和數據** 由 `:` 分隔，**單元格** 由 `,` 分隔。
```json
"full_content": [
  {
    "type": "table",
    "content": "x: 0, 1, 2; P(X=x): 1-4p, aP, p"
  }
]
```

#### 5.3 圖片 (images)
- 在對應題目的 `images` 數組中，添加圖片的文件名（通常是一個長哈希值）。
- 在根目錄的 `images` 數組中，添加圖片的詳細信息對象。
```json
// 在題目中
"images": ["8e6233fb71396caa66128b8c506297534f65c66a9fadb11849f0901cb92c3c16"],

// 在根目錄
"images": [
  {
    "id": 1,
    "filename": "8e6233fb71396caa66128b8c506297534f65c66a9fadb11849f0901cb92c3c16",
    "file_extension": "jpg",
    "alt_text": "Image from paper",
    "description": "Image included in question 10.",
    "file_path": "images/8e6233fb71396caa66128b8c506297534f65c66a9fadb11849f0901cb92c3c16.jpg"
  }
]
```

## 🎯 主題編碼分配指南
(此部分保持不變)
### 微積分主題 (CAL)
- **CAL-LIM**: 函數的極限、泰勒展開
- **CAL-DER**: 導數計算、鏈式法則、對數微分
- **CAL-APP**: 導數應用、極值、優化、相關變化率
- **CAL-INT**: 不定積分、定積分計算
- **CAL-AIN**: 積分應用、面積、體積計算

### 統計主題 (STA)
- **STA-PRO**: 基本概率、條件概率、貝葉斯定理
- **STA-DIS**: 離散概率分佈、期望值、方差
- **STA-CON**: 連續概率分佈、泊松分佈、指數分佈
- **STA-NOR**: 正態分佈、標準化、中央極限定理
- **STA-SAM**: 抽樣分佈、參數估計、置信區間
- **STA-HYP**: 假設檢驗、p值、檢驗統計量

## 🔍 質量檢查清單
轉換完成後，請檢查：
- [ ] JSON語法有效。
- [ ] 所有題目都有正確的主題編碼。
- [ ] **子題的 `part` 欄位保留了完整的父級結構（如 `a(i)`）。**
- [ ] 分數計算正確（子題分數總和應等於主題總分）。
- [ ] 圖片文件名和路徑正確無誤。
- [ ] 數學公式格式正確且已轉義。
- [ ] 表格數據格式符合規範。

**記住：質量勝過速度。寧可多花時間確保準確性，也不要匆忙完成而留下錯誤。**
