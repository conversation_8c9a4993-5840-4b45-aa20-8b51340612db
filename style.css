body {
    font-family: 'Microsoft JhengHei', 'PingFang TC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f7f6;
    color: #333;
}

.container {
    max-width: 950px;
    margin: 0 auto;
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
}

.search-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 30px;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.search-controls select,
.search-controls input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 1em;
}

#results-container {
    display: grid;
    grid-template-columns: 1fr; /* Default to single column for detailed view */
    gap: 20px;
}

.result-item {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.result-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

/* Card styles for the default paper view */
#results-container.paper-cards {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.paper-card h3 {
    margin-top: 0;
    font-size: 1.2em;
}

.paper-card h3 a {
    text-decoration: none;
    color: #3498db;
    font-weight: bold;
}

.paper-card h3 a:hover {
    text-decoration: underline;
}


/* Detailed question view styles (from viewer) */
.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.question-number {
    background-color: #e74c3c;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: bold;
}

.question-marks {
    background-color: #27ae60;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9em;
}

.topic-info {
    background-color: #f39c12;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    margin-left: 10px;
}

.question-content {
    margin-bottom: 20px;
    font-size: 1.1em;
    line-height: 1.8;
}

.sub-questions-container {
    margin-top: 20px;
    padding-left: 15px;
    border-left: 2px solid #e0e0e0;
}

.sub-question {
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-left: 4px solid #3498db;
    border-radius: 0 8px 8px 0;
}

.sub-question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.sub-question-part {
    font-weight: bold;
    color: #2c3e50;
    background-color: #ecf0f1;
    padding: 4px 8px;
    border-radius: 8px;
}

.sub-question-marks {
    background-color: #f39c12;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
}

.sub-question-content {
    margin-top: 8px;
}

.sub-question.depth-1 {
    margin-left: 25px;
    border-left-color: #e67e22;
}

.sub-question.depth-2 {
    margin-left: 50px;
    border-left-color: #9b59b6;
}

.question-image {
    text-align: center;
    margin: 20px 0;
}

.question-image img {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.data-table {
    margin: 20px 0;
    border-collapse: collapse;
    width: 100%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.data-table th,
.data-table td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: center;
    font-size: 1.1em;
}

.data-table th {
    background-color: #34495e;
    color: white;
    font-weight: bold;
}

.data-table td {
    background-color: white;
}

.data-table tr:nth-child(even) td {
    background-color: #f8f9fa;
}