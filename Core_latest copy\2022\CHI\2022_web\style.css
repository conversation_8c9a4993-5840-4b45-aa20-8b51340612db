body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
    color: #333;
}

.container {
    max-width: 800px;
    margin: auto;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

header h1 {
    text-align: center;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.section {
    margin-bottom: 30px;
}

.section-title {
    background-color: #3498db;
    color: white;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.question {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #fafafa;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: bold;
}

.question-id {
    font-size: 1.2em;
    color: #2980b9;
}

.topic-info {
    font-size: 0.9em;
    color: #7f8c8d;
}

.question-content {
    margin-bottom: 10px;
}

.question-image img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.sub-questions-container {
    margin-left: 20px;
    border-left: 2px solid #3498db;
    padding-left: 15px;
}

.sub-question {
    margin-top: 15px;
}

.sub-question-header {
    display: flex;
    align-items: center;
    font-weight: bold;
}

.sub-question-part {
    margin-right: 10px;
    color: #c0392b;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.data-table th, .data-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

.data-table th {
    background-color: #f2f2f2;
}