body {
    font-family: 'Microsoft JhengHei', 'PingFang TC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}
.container {
    max-width: 900px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}
.header {
    text-align: center;
    border-bottom: 3px solid #2c3e50;
    padding-bottom: 20px;
    margin-bottom: 30px;
}
.header h1 {
    color: #2c3e50;
    margin: 0;
    font-size: 2.5em;
}
.header .subtitle {
    color: #7f8c8d;
    font-size: 1.2em;
    margin: 10px 0;
}
.paper-info {
    background-color: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}
.paper-info h2 {
    margin-top: 0;
    color: #34495e;
}
.statistics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}
.stat-item {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #3498db;
}
.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: #2c3e50;
}
.stat-label {
    color: #7f8c8d;
    font-size: 0.9em;
}
.section {
    margin-bottom: 40px;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    overflow: hidden;
}
.section-header {
    background-color: #3498db;
    color: white;
.topic-info {
    background-color: #f39c12;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    margin-left: 10px;
}
    padding: 15px;
    font-size: 1.3em;
    font-weight: bold;
}
.question {
    border-bottom: 1px solid #ecf0f1;
    padding: 25px;
}
.question:last-child {
    border-bottom: none;
}
.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}
.question-number {
    background-color: #e74c3c;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: bold;
}
.question-marks {
    background-color: #27ae60;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9em;
}
.topic-info {
    background-color: #f39c12;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    margin-left: 10px;
}
.question-content {
    margin-bottom: 20px;
    font-size: 1.1em;
    line-height: 1.8;
}
.sub-questions-container {
    margin-top: 20px;
    padding-left: 15px;
    border-left: 2px solid #e0e0e0;
}

.sub-question {
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-left: 4px solid #3498db;
    border-radius: 0 8px 8px 0;
}

.sub-question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.sub-question-part {
    font-weight: bold;
    color: #2c3e50;
    background-color: #ecf0f1;
    padding: 4px 8px;
    border-radius: 8px;
}

.sub-question-marks {
    background-color: #f39c12;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
}

.sub-question-content {
    margin-top: 8px;
}

/* Indentation and different color for nested levels */
.sub-question.depth-1 {
    margin-left: 25px;
    border-left-color: #e67e22;
}
.sub-question.depth-2 {
    margin-left: 50px;
    border-left-color: #9b59b6;
}
.question-image {
    text-align: center;
    margin: 20px 0;
}
.question-image img {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.image-description {
    font-style: italic;
    color: #7f8c8d;
    margin-top: 10px;
    font-size: 0.9em;
}
.table-content {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    border-left: 4px solid #9b59b6;
}
.data-table {
    margin: 20px 0;
    border-collapse: collapse;
    width: 100%;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.data-table th,
.data-table td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: center;
    font-size: 1.1em;
}
.data-table th {
    background-color: #34495e;
    color: white;
    font-weight: bold;
}
.data-table td {
    background-color: white;
}
.data-table tr:nth-child(even) td {
    background-color: #f8f9fa;
}
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    .header h1 {
        font-size: 2em;
    }
    .question-header {
        flex-direction: column;
        align-items: flex-start;
    }
    .statistics {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}