{"year": 2020, "lang": "ENG", "subject": "M1", "title": "MATHEMATICS Extended Part Module 1 Calculus and Statistics", "image_base_path": "2020-Sample-MATH-M1-Level1-E.pdf-49d2dc56-45a3-4ea8-a3df-5fa4755ecf7a", "sections": [{"id": "A", "title": "SECTION A", "total_marks": 50, "questions": [{"id": "1", "marks": 6, "topic": {"code": "STA-DIS", "name": "Discrete Random Variables"}, "content": "The table below shows the probability distribution of a discrete random variable \\(X\\), where \\(a\\) is a constant and \\(p > 0\\).<br><table><tr><td>x</td><td>0</td><td>1</td><td>2</td></tr><tr><td>P(X=x)</td><td>1-4p</td><td>ap</td><td>p</td></tr></table>", "sub_questions": [{"id": "a", "marks": null, "content": "Find \\(a\\)."}, {"id": "b", "marks": null, "content": "If \\(\\operatorname {Var}(2X + a^2) = 8\\operatorname {E}(aX - 1)\\), find \\(p\\)."}], "images": []}, {"id": "2", "marks": 6, "topic": {"code": "STA-DIST", "name": "Probability Distributions"}, "content": "Using photocopier \\(X\\), the probability that there is dirt on a page of photocopy is \\(\\frac{1}{5}\\). A document of 6 pages is photocopied repeatedly by using \\(X\\).", "sub_questions": [{"id": "a", "marks": null, "content": "Find the probability that there is dirt on a photocopy of the document."}, {"id": "b", "marks": null, "content": "A photocopy of the document is called acceptable if there is dirt on fewer than 3 pages, otherwise the photocopy is called unacceptable.", "sub_questions": [{"id": "i", "marks": null, "content": "Find the probability that a photocopy of the document is unacceptable."}, {"id": "ii", "marks": null, "content": "If an unacceptable photocopy of the document is just produced, find the expected number of acceptable photocopies produced between this unacceptable photocopy and the next unacceptable photocopy."}]}], "images": []}, {"id": "3", "marks": 7, "topic": {"code": "STA-PROB", "name": "Conditional Probability"}, "content": "Let \\(A\\) and \\(B\\) be two events. Denote the complementary event of \\(A\\) by \\(A'\\). It is given that \\(\\mathbb{P}(B\\mid A) = \\frac{1}{2}\\) and \\(\\mathbb{P}(B) = \\frac{1}{3} +\\mathbb{P}(A)\\). Suppose that \\(\\mathbb{P}(A'\\cap B) = k\\mathbb{P}(A)\\), where \\(k\\) is a constant.", "sub_questions": [{"id": "a", "marks": null, "content": "By considering \\(\\mathbb{P}(A\\cap B)\\), or otherwise, prove that \\(k\\neq \\frac{1}{2}\\). Also express \\(\\mathbb{P}(B)\\) in terms of \\(k\\)."}, {"id": "b", "marks": null, "content": "Are \\(A\\) and \\(B\\) mutually exclusive? Explain your answer."}, {"id": "c", "marks": null, "content": "If \\(A\\) and \\(B\\) are independent, find \\(k\\)."}], "images": []}, {"id": "4", "marks": 6, "topic": {"code": "STA-CI", "name": "Confidence Intervals"}, "content": "A magazine publisher wants to estimate the proportion \\(p\\) of its current subscribers who will continue to subscribe next year. It is given that the publisher randomly selects 841 current subscribers, and 441 of them will continue to subscribe.", "sub_questions": [{"id": "a", "marks": null, "content": "Find an approximate \\(95%\\) confidence interval for \\(p\\)."}, {"id": "b", "marks": null, "content": "An approximate \\(\\beta%\\) confidence interval for \\(p\\) is now constructed. The width of the confidence interval is 0.088. Find \\(\\beta\\) correct to the nearest integer."}], "images": []}, {"id": "5", "marks": 6, "topic": {"code": "CAL-BINE", "name": "Binomial Expansion & e"}, "content": "Let \\(\\mathbf{f}(x) = (1 + k e^{x})^{3}\\), where \\(k\\) is a real constant.", "sub_questions": [{"id": "a", "marks": null, "content": "Express, in terms of \\(k\\), the constant term and the coefficient of \\(x^{2}\\) in the expansion of \\(\\mathbf{f}(x)\\)."}, {"id": "b", "marks": null, "content": "If the constant term in the expansion of \\(\\mathbf{f}(x)\\) is 27, find the coefficient of \\(x^{2}\\) in this expansion."}], "images": []}, {"id": "6", "marks": 6, "topic": {"code": "CAL-APP", "name": "Applications of Differentiation"}, "content": "Define \\(g(x) = x + \\frac{5}{x} + \\ln x^4\\) for all non-zero real numbers \\(x\\).", "sub_questions": [{"id": "a", "marks": null, "content": "Find \\(g'(x)\\)."}, {"id": "b", "marks": null, "content": "Someone claims that the maximum value of \\(g(x)\\) is less than the minimum value of \\(g(x)\\). Do you agree? Explain your answer."}, {"id": "c", "marks": null, "content": "Write down the equations of the two horizontal tangents to the graph of \\(y = g(x)\\)."}], "images": []}, {"id": "7", "marks": 6, "topic": {"code": "CAL-APP", "name": "Applications of Differentiation"}, "content": "The total surface area of a solid right circular cylinder is \\(486\\pi \\mathrm{cm}^2\\).", "sub_questions": [{"id": "a", "marks": null, "content": "Let \\(V\\mathrm{cm}^3\\) and \\(r\\mathrm{cm}\\) be the volume and the base radius of the circular cylinder respectively. Find \\(\\frac{\\mathrm{d}V}{\\mathrm{d}r}\\)."}, {"id": "b", "marks": null, "content": "Can the volume of the circular cylinder exceed \\(5000 \\mathrm{cm}^3\\)? Explain your answer."}], "images": []}, {"id": "8", "marks": 7, "topic": {"code": "CAL-INT", "name": "Integration"}, "content": "Let m be a non-zero constant.", "sub_questions": [{"id": "a", "marks": null, "content": "By considering \\(\\frac{\\mathrm{d}}{\\mathrm{d}x} (x e^{mx})\\), find \\(\\int x e^{mx} \\mathrm{d}x\\)."}, {"id": "b", "marks": null, "content": "If the area of the region bounded by the curve \\(y = x e^{mx}\\), the \\(x\\)-axis and the straight line \\(x = 1\\) is \\(\\frac{1}{m}\\), find \\(m\\)."}], "images": []}]}, {"id": "B", "title": "SECTION B", "total_marks": 50, "questions": [{"id": "9", "marks": 13, "topic": {"code": "STA-NORM", "name": "Normal Distribution"}, "content": "Every morning <PERSON> leaves home at 7:10 and walks to a certain bus stop to catch bus. He catches the earliest departing bus when he arrives at the bus stop. The time taken for <PERSON>'s walk follows a normal distribution with a mean of 15 minutes and a standard deviation of 2 minutes. There are two buses each morning, departing at 7:23 and 7:30 respectively.", "sub_questions": [{"id": "a", "marks": 2, "content": "Find the probability that <PERSON> catches the bus departing at 7:23 on a certain morning."}, {"id": "b", "marks": 1, "content": "Find the probability that <PERSON> catches the bus departing at 7:30 on a certain morning."}, {"id": "c", "marks": 10, "content": "Every morning <PERSON>, <PERSON>'s student, walks to the same bus stop to catch bus. She catches the earliest departing bus when she arrives at the bus stop. It is given that the probability that <PERSON> catches the bus departing at 7:23 on a certain morning is 0.3015, while the probability that she catches the bus departing at 7:30 on a certain morning is 0.6328. If <PERSON> and <PERSON> catch the same bus, <PERSON> will greet <PERSON>.", "sub_questions": [{"id": "i", "marks": null, "content": "Find the probability that the 4th day in a week is the 2nd time <PERSON> greets <PERSON>."}, {"id": "ii", "marks": null, "content": "Given that <PERSON> greets <PERSON> on 2 certain mornings, find the probability that <PERSON> and <PERSON> catch the bus departing at 7:30 on these 2 mornings."}, {"id": "iii", "marks": null, "content": "Given that <PERSON> greets <PERSON> on 4 certain mornings, find the probability that <PERSON> and <PERSON> catch the bus departing at 7:23 on at least 1 of these 4 mornings."}, {"id": "iv", "marks": null, "content": "If <PERSON> wants to have a higher chance of catching the bus departing at 7:23 than that of <PERSON>, what is the latest time for him to leave home? Give your answer correct to the nearest minute."}]}], "images": []}, {"id": "10", "marks": 12, "topic": {"code": "STA-DIST", "name": "Probability Distributions"}, "content": "A shopping mall launches a campaign to celebrate its fifth anniversary of the opening. A customer can throw a fair die 4 times to receive cash coupons. For each throw, a cash coupon is awarded according to the following table:<br><table><tr><td>Result</td><td>1, 2 or 3</td><td>4 or 6</td><td>5</td></tr><tr><td>Value of cash coupon</td><td>$10</td><td>$25</td><td>$50</td></tr></table>", "sub_questions": [{"id": "a", "marks": 1, "content": "Find the probability that a customer receives cash coupons of a total value $200."}, {"id": "b", "marks": 2, "content": "Find the probability that a customer receives cash coupons of a total value not less than $150."}, {"id": "c", "marks": 9, "content": "A customer who receives cash coupons of a total value not less than $150 can join a game. In the game, the customer presses a button 3 times. A number of cakes will show up on a screen for each press of the button, and the number of cakes shown follows a Poisson distribution with a mean of 5. The result of each press of button is classified as follows:<br><table><tr><td>Number of cakes</td><td>1 to 4</td><td>5</td><td>otherwise</td></tr><tr><td>Result</td><td>Good</td><td>Excellent</td><td>Fair</td></tr></table><br><table><tr><td>Result</td><td>1 Excellent and 2 Good</td><td>2 Excellent and 1 Good</td><td>3 Excellent</td></tr><tr><td>Prize</td><td>a cup</td><td>a backpack</td><td>an oven</td></tr></table>", "sub_questions": [{"id": "i", "marks": null, "content": "Find the probability that a customer joining the game receives a backpack."}, {"id": "ii", "marks": null, "content": "Given that a customer joining the game receives a prize, find the probability that the customer receives an oven."}, {"id": "iii", "marks": null, "content": "A customer who cannot join the game can still receive a cup by joining a lucky draw. In the lucky draw, the probability of receiving a cup is 0.01. Given that a customer receives a cup, find the probability that the customer cannot join the game."}]}], "images": []}, {"id": "11", "marks": 12, "topic": {"code": "CAL-TRAP", "name": "Trapezoidal Rule"}, "content": "The manager of a park models the rate of change of the number of adults (in thousand per month) visiting the park by\n\\(\\mathrm{A}(t) = 5\\ln (t^{2} - 8t + 20),\\)\nwhere \\(t\\) is the number of months elapsed since the park opens. Denote the total number of adults visiting the park in the first 2 months since the park opens by \\(\\alpha\\) thousand. Let \\(\\alpha_{1}\\) be the estimate of \\(\\alpha\\) by using the trapezoidal rule with 5 sub- intervals.", "sub_questions": [{"id": "a", "marks": 5, "content": "", "sub_questions": [{"id": "i", "marks": null, "content": "Find \\(\\alpha_{1}\\)."}, {"id": "ii", "marks": null, "content": "Is \\(\\alpha_{1}\\) an over- estimate or an under- estimate? Explain your answer."}]}, {"id": "b", "marks": 7, "content": "The manager models the rate of change of the number of children (in thousand per month) visiting the park by\n\\(\\mathbf{B}(t) = \\frac{3^{2t + 2}}{1 + 3^{2t}},\\)\nwhere \\(t\\) is the number of months elapsed since the park opens.", "sub_questions": [{"id": "i", "marks": null, "content": "Find the total number of children visiting the park in the first 2 months since the park opens."}, {"id": "ii", "marks": null, "content": "The manager claims that in the first 2 months since the park opens, the difference of the total number of adults visiting the park and the total number of children visiting the park exceeds 40% of the total number of adults visiting the park. Do you agree? Explain your answer."}]}], "images": []}, {"id": "12", "marks": 13, "topic": {"code": "CAL-EXPLOG", "name": "Exponential/Logarithmic Functions"}, "content": "<PERSON> studies the number of ducks \\(P\\) (in thousand) in a farm by\n\\(P = \\frac{32}{a^{5 + bt} + 8},\\)\nwhere \\(a\\) and \\(b\\) are constants and \\(t (t \\geq 0)\\) is the number of months elapsed since the start of the study.", "sub_questions": [{"id": "a", "marks": 2, "content": "Express \\(\\ln \\left(\\frac{32}{P} - 8\\right)\\) as a linear function of \\(t\\)."}, {"id": "b", "marks": 11, "content": "<PERSON> finds that the graph of the linear function obtained in (a) passes through the point \\((1, \\ln 2)\\) and the intercept on the vertical axis of the graph is \\(\\ln 32\\).", "sub_questions": [{"id": "i", "marks": null, "content": "Find \\(a\\) and \\(b\\)."}, {"id": "ii", "marks": null, "content": "Find \\(\\frac{\\mathrm{d}P}{\\mathrm{d}t}\\) and \\(\\frac{\\mathrm{d}^2P}{\\mathrm{d}t^2}\\)."}, {"id": "iii", "marks": null, "content": "Estimate the number of ducks in the farm after a very long time. Hence, or otherwise, prove that the number of ducks in the farm does not exceed 4 thousand since the start of the study."}, {"id": "iv", "marks": null, "content": "Find the number of ducks in the farm when \\(\\frac{\\mathrm{d}P}{\\mathrm{d}t}\\) attains its greatest value."}]}], "images": []}]}], "images": []}