{"year": 2021, "lang": "ENG", "subject": "M1", "title": "MATHEMATICS Extended Part Module 1 Calculus and Statistics", "image_base_path": "2021-Sample-MATH-M1-Level1-E.pdf-99c220f2-7a7e-4f27-a770-a33d8d2eec25", "sections": [{"id": "A", "title": "SECTION A", "total_marks": 50, "questions": [{"id": "1", "marks": 6, "topic": {"code": "STA-DIS", "name": "Discrete Random Variables"}, "content": "The table below shows the probability distribution of a discrete random variable \\(X\\), where \\(a\\) and \\(b\\) are constants.<br><table><tr><td>x</td><td>-1</td><td>0</td><td>1</td><td>2</td><td>3</td><td>4</td></tr><tr><td>P(X=x)</td><td>a</td><td>0.1</td><td>0.15</td><td>0.15</td><td>b</td><td>0.05</td></tr></table><br>It is given that \\(\\operatorname {E}(5X + 1) = 10\\).", "sub_questions": [{"id": "a", "marks": null, "content": "Find \\(a\\) and \\(b\\)."}, {"id": "b", "marks": null, "content": "Let \\(C\\) be the event that \\(X > 0\\) and \\(D\\) be the event that \\(X \\leq 2\\). Find \\(\\operatorname {P}(C \\mid D)\\)."}], "images": []}, {"id": "2", "marks": 6, "topic": {"code": "STA-PROB", "name": "Conditional Probability"}, "content": "The probability that a person has disease \\(D\\) is 0.12. Test \\(T\\) is used to show whether a person has disease \\(D\\) or not. For a person who has disease \\(D\\), the probability that test \\(T\\) shows that the person has disease \\(D\\) is 0.97. For a person who does not have disease \\(D\\), the probability that test \\(T\\) shows that the person does not have disease \\(D\\) is 0.89.", "sub_questions": [{"id": "a", "marks": null, "content": "Find the probability that test \\(T\\) shows a correct result."}, {"id": "b", "marks": null, "content": "Find the probability that test \\(T\\) shows that a person has disease \\(D\\)."}, {"id": "c", "marks": null, "content": "Given that a person is shown to have disease \\(D\\) by test \\(T\\), is the probability that the person actually has disease \\(D\\) less than 0.6? Explain your answer."}], "images": []}, {"id": "3", "marks": 7, "topic": {"code": "STA-DIST", "name": "Probability Distributions"}, "content": "In an examination, there are 10 questions. For each question, the probability that <PERSON> knows how to do the question is 0.8. For each question that <PERSON> knows how to do, the probability that he carelessly answers the question wrongly is 0.1; otherwise, <PERSON> will answer the question correctly for the question that he knows how to do. For questions that <PERSON> does not know how to do, he will answer them wrongly. <PERSON> gets grade A if he answers 8 or more questions correctly.", "sub_questions": [{"id": "a", "marks": null, "content": "Find the probability that <PERSON> gets grade A."}, {"id": "b", "marks": null, "content": "Find the probability that <PERSON> knows how to do all the questions and gets grade A."}, {"id": "c", "marks": null, "content": "Given that <PERSON> gets grade A, find the probability that he knows how to do all the questions."}], "images": []}, {"id": "4", "marks": 6, "topic": {"code": "STA-CI", "name": "Confidence Intervals"}, "content": "<PERSON> conducts a survey to estimate the proportion \\(p\\) of children in a city who learn recorder. In a random sample of 40 children from the city, 28 of them learn recorder.", "sub_questions": [{"id": "a", "content": "", "sub_questions": [{"id": "i", "marks": null, "content": "Find the sample proportion of children who learn recorder."}, {"id": "ii", "marks": null, "content": "Find an approximate \\(90\\%\\) confidence interval for \\(p\\)."}]}, {"id": "b", "marks": null, "content": "<PERSON> now wants to construct an approximate \\(99\\%\\) confidence interval for \\(p\\) such that the width of the confidence interval does not exceed 0.1. Using the result of (a)(i), estimate the least number of children that <PERSON> should survey."}], "images": []}, {"id": "5", "marks": 6, "topic": {"code": "CAL-INT", "name": "Integration"}, "content": "Let \\(\\mathbf{f}(x) = e^{-x^{\\frac{1}{3}}}\\).", "sub_questions": [{"id": "a", "marks": null, "content": "Let \\(\\mathbf{g}(u) = e^{-u}(u^{2} + 2u + 2)\\), where \\(u = x^{\\frac{1}{3}}\\). Find the constant \\(\\beta\\) such that \\(\\frac{\\mathrm{d}\\mathbf{g}(u)}{\\mathrm{d}x} = \\beta \\mathbf{f}(x)\\)."}, {"id": "b", "marks": null, "content": "Express, in terms of \\(e\\), the area of the region bounded by the curve \\(y = \\mathbf{f}(x)\\), the \\(x\\)-axis, the \\(y\\)-axis and the straight line \\(x = 8\\)."}], "images": []}, {"id": "6", "marks": 5, "topic": {"code": "CAL-BINE", "name": "Binomial Expansion & e"}, "content": "", "sub_questions": [{"id": "a", "marks": null, "content": "Expand \\(e^{-6x}\\) in ascending powers of \\(x\\) as far as the term in \\(x^4\\)."}, {"id": "b", "marks": null, "content": "Find the constant \\(k\\) such that the coefficient of \\(x^4\\) in the expansion of \\(e^{-6x} (1 - kx^2)^5\\) is \\(-26\\)."}], "images": []}, {"id": "7", "marks": 7, "topic": {"code": "CAL-APP", "name": "Applications of Differentiation"}, "content": "Let \\(y = \\frac{e^x}{x^3 - x + 2}\\), where \\(0 \\leq x \\leq 5\\). Find", "sub_questions": [{"id": "a", "marks": null, "content": "\\(\\frac{\\mathrm{d}y}{\\mathrm{d}x}\\),"}, {"id": "b", "marks": null, "content": "the greatest value and the least value of \\(y\\)."}], "images": []}, {"id": "8", "marks": 7, "topic": {"code": "CAL-INT", "name": "Integration"}, "content": "Let \\(\\mathbf{f}(x)\\) be a function such that \\(\\mathbf{f}'(x) = \\frac{2^{kx}}{1 + 2^{kx}}\\), where \\(k\\) is a constant. The straight line \\(8x - 9y + 10 = 0\\) touches the curve \\(y = \\mathbf{f}(x)\\) at the point \\(A\\). It is given that the \\(x\\)-coordinate of \\(A\\) is 1. Find", "sub_questions": [{"id": "a", "marks": null, "content": "\\(k\\)"}, {"id": "b", "marks": null, "content": "\\(\\mathbf{f}(x)\\)"}], "images": []}]}, {"id": "B", "title": "SECTION B", "total_marks": 50, "questions": [{"id": "9", "marks": 11, "topic": {"code": "STA-NORM", "name": "Normal Distribution"}, "content": "The weight of each potato in a large farm follows a normal distribution with a mean of 200 grams and a standard deviation of \\(\\sigma\\) grams. The classification of the potatoes is as follows:<br><table><tr><td>Weight of a potato (W grams)</td><td>W < 180</td><td>180 ≤ W < 230</td><td>W ≥ 230</td></tr><tr><td>Classification</td><td>small</td><td>medium</td><td>big</td></tr></table><br>It is given that \\(21.19\\%\\) of the potatoes in the farm are small.", "sub_questions": [{"id": "a", "marks": 3, "content": "Find the percentage of medium potatoes in the farm."}, {"id": "b", "marks": 3, "content": "The potatoes in the farm are now inspected one by one. Find the probability that the 4th potato inspected is the 2nd big potato inspected."}, {"id": "c", "marks": 5, "content": "From the farm, 5 potatoes are randomly selected.", "sub_questions": [{"id": "i", "marks": null, "content": "Find the probability that there are exactly 1 big potato and 2 small potatoes."}, {"id": "ii", "marks": null, "content": "Given that there is exactly 1 big potato, find the probability that there are at least 2 small potatoes."}]}], "images": []}, {"id": "10", "marks": 14, "topic": {"code": "STA-POI", "name": "Poisson Distribution"}, "content": "The number of commercial emails that <PERSON> receives each hour follows a Poisson distribution with a mean of 1.3 per hour, while the number of non-commercial emails that he receives each hour follows a Poisson distribution with a mean of 0.9 per hour.", "sub_questions": [{"id": "a", "marks": 3, "content": "Find the probability that the number of non-commercial emails that <PERSON> receives in a certain hour is fewer than 3."}, {"id": "b", "marks": 2, "content": "Find the probability that the number of commercial emails that <PERSON> receives in 6 hours is 5."}, {"id": "c", "marks": 3, "content": "Find the probability that the number of emails that <PERSON> receives in a certain hour is 2."}, {"id": "d", "marks": 3, "content": "Given that the number of emails that <PERSON> receives in a certain hour is 2, find the probability that both emails are non-commercial emails."}, {"id": "e", "marks": 3, "content": "Given that the number of emails that <PERSON> receives in a certain hour is fewer than 3, find the probability that <PERSON> does not receive commercial email in that hour."}], "images": []}, {"id": "11", "marks": 11, "topic": {"code": "CAL-TRAP", "name": "Trapezoidal Rule"}, "content": "Let \\(\\mathbf{f}(x) = \\left(\\frac{x}{2 - x}\\right)^{\\frac{1}{2}}\\), where \\(0 \\leq x \\leq 1\\).", "sub_questions": [{"id": "a", "marks": 3, "content": "Find \\(\\mathbf{f}'(x)\\) and \\(\\mathbf{f}''(x)\\)."}, {"id": "b", "marks": 8, "content": "Define \\(J = \\int_{0}^{0.5} \\mathbf{f}(x) \\mathrm{d}x\\) and \\(K = \\int_{0.5}^{1} \\mathbf{f}(x) \\mathrm{d}x\\).", "sub_questions": [{"id": "i", "marks": null, "content": "Using the trapezoidal rule with 5 sub-intervals, estimate \\(J\\)."}, {"id": "ii", "marks": null, "content": "Using the fact that \\(\\int_{0}^{1} \\mathbf{f}(x) \\mathrm{d}x = \\frac{\\pi - 2}{2}\\) and the result of (b)(i), estimate \\(K\\)."}, {"id": "iii", "marks": null, "content": "Someone claims that \\(\\frac{J}{K} < 0.44\\). Do you agree? Explain your answer."}]}], "images": []}, {"id": "12", "marks": 14, "topic": {"code": "CAL-APP", "name": "Applications of Integration"}, "content": "A tank is used for collecting rain water. During a certain shower, rain water flows into the tank for 7 minutes. Let \\(V\\mathbf{m}^{3}\\) be the volume of rain water in the tank. It is given that\n\\(\n\\frac{\\mathrm{d}V}{\\mathrm{d}t} = \\sqrt{t + 1}\\sqrt{3 - \\sqrt{t + 1}}\\qquad (0\\leq t\\leq 7),\n\\)\nwhere \\(t\\) is the number of minutes elapsed since rain water starts flowing into the tank. The tank is empty at \\(t = 0\\) and the rate of change of the volume of rain water in the tank attains its maximum value when \\(t = T\\).", "sub_questions": [{"id": "a", "marks": 4, "content": "Find \\(T\\)."}, {"id": "b", "marks": 5, "content": "Find the exact value of \\(V\\) when \\(t = T\\)."}, {"id": "c", "marks": 5, "content": "The tank is in the shape of an inverted right circular cone of height \\(1\\mathbf{m}\\) and base radius \\(6\\mathbf{m}\\). The tank is held vertically. Let \\(h\\mathbf{m}\\) be the depth of rain water in the tank. Find", "sub_questions": [{"id": "i", "marks": null, "content": "the constant \\(Q\\) such that \\(\\frac{\\mathrm{d}V}{\\mathrm{d}t} = Q h^{2}\\frac{\\mathrm{d}h}{\\mathrm{d}t}\\)"}, {"id": "ii", "marks": null, "content": "\\(\\left.\\frac{\\mathrm{d}h}{\\mathrm{d}t}\\right|_{t = T}\\)."}]}], "images": []}]}], "images": []}