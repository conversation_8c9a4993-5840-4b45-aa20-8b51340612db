erDiagram
    SUBJECTS {
        varchar(15) subject_code PK "Subject code, e.g., MATH_M1"
    }

    SYLLABUS {
        int id PK
        varchar(15) subject_code FK "To SUBJECTS"
        varchar(255) name "e.g., DSE M1 2016-2024"
        smallint effective_year_start
        smallint effective_year_end "Nullable for current"
    }

    TOPICS {
        int id PK
        int syllabus_id FK "To SYLLABUS"
        varchar(20) topic_code "Human-readable code, e.g., CAL-LIM"
        varchar(255) name
        int parent_topic_id FK "Self-referencing to TOPICS.id"
    }

    QUESTIONS {
        varchar(40) id PK "Language-specific ID, e.g., MATH_M1-2022-EN-1"
        varchar(50) group_id "Links EN/CH versions, from JSON's question_id"
        int syllabus_id FK "To SYLLABUS"
        int main_topic_id FK "To TOPICS"
        smallint year
        smallint number
        varchar(3) language "EN or CH"
        text content_md
        smallint total_marks
    }

    SUB_QUESTIONS {
        serial id PK
        varchar(40) question_id FK "To QUESTIONS"
        varchar(5) part "e.g., a, b, c"
        text content_md
        smallint marks
    }

    QUESTION_TOPIC_MAPPING {
        serial id PK
        varchar(40) question_id FK "To QUESTIONS"
        int sub_question_id FK "To SUB_QUESTIONS (nullable)"
        int topic_id FK "To TOPICS"
    }

    SUBJECTS ||--|{ SYLLABUS : "has"
    SYLLABUS ||--|{ TOPICS : "defines"
    SYLLABUS ||--|{ QUESTIONS : "applies to"
    TOPICS }o--o{ TOPICS : "is parent of"
    TOPICS ||--|{ QUESTIONS : "classifies as main"
    QUESTIONS ||--o{ SUB_QUESTIONS : "contains"
    QUESTIONS ||--|{ QUESTION_TOPIC_MAPPING : "maps to question"
    SUB_QUESTIONS ||--|{ QUESTION_TOPIC_MAPPING : "maps to sub-question"
    TOPICS ||--|{ QUESTION_TOPIC_MAPPING : "is mapped by"

