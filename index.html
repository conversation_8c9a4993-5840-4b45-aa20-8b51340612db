<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DSE Paper Search</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://polyfill.alicdn.com/v3/polyfill.min.js?features=es6"></script>
    <script src="script.js"></script>
   <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
   <script>
       window.MathJax = {
           tex: {
               inlineMath: [['$', '$'], ['\\(', '\\)']],
               displayMath: [['$$', '$$'], ['\\[', '\\]']]
           },
           startup: {
               ready: () => {
                   console.log('MathJax is ready');
                   window.MathJax.startup.defaultReady();
               }
           }
       };
   </script>
</head>
<body>
    <div class="container">
        <h1>DSE Paper Search</h1>
        <div class="search-controls">
            <select id="subject-filter">
                <option value="">All Subjects</option>
                <option value="M1">M1</option>
                <option value="Core">Core</option>
            </select>
            <select id="year-filter">
                <option value="">All Years</option>
                <option value="2024">2024</option>
                <option value="2023">2023</option>
                <option value="2022">2022</option>
                <option value="2021">2021</option>
                <option value="2020">2020</option>
            </select>
            <select id="lang-filter">
                <option value="">All Languages</option>
                <option value="ENG">English</option>
                <option value="CHI">Chinese</option>
            </select>
            <select id="topic-filter">
               <option value="">All Topics</option>
            </select>
            <input type="text" id="keyword-search" placeholder="Search keywords...">
        </div>
        <div id="results-container"></div>
    </div>
</body>
</html>