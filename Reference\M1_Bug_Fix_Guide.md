# M1 試卷瀏覽器調試與修復指南

本文檔詳細記錄了在修復 M1 試卷網頁瀏覽器時遇到的一系列問題及其解決方案。旨在為未來的維護人員或 AI 提供清晰的上下文和可行的解決步驟。

## 總結

最初的問題是多個年份的試卷頁面顯示為空白。在修復此問題後，又出現了兩個後續問題：部分頁面缺少 CSS 樣式，以及所有頁面上的數學公式（使用 LaTeX 編寫）無法正確渲染。

通過以 **2023 M1 English** 頁面作為測試案例，我們逐步診斷並解決了所有問題。核心解決方案涉及修復 HTML 結構、確保腳本載入順序，以及規範化數據源中的 LaTeX 語法。

---

## 問題一：頁面顯示空白

-   **症狀**: 訪問某些試卷的 `index.html` 時，頁面完全空白，控制台無明顯錯誤。
-   **根本原因**: 渲染腳本 `script.js` 需要在 HTML 中找到一個 ID 為 `paper-content` 的 `<main>` 元素，以便將從 JSON 檔案中獲取的試卷內容注入其中。在所有損壞的 HTML 檔案中，這個 `<main>` 元素都缺失了。
-   **解決方案**:
    在每個受影響的 `index.html` 檔案的 `<body>` 標籤內，手動添加以下元素：
    ```html
    <main id="paper-content"></main>
    ```
    這個修復讓試卷內容得以初步載入，但也暴露了後續的問題。

---

## 問題二：CSS 樣式丟失

-   **症狀**: 在修復問題一後，部分頁面（如 2023 M1 English）雖然顯示了內容，但完全沒有 CSS 樣式，呈現為純 HTML 格式。
-   **根本原因**: 經檢查，受影響頁面的 `.../web/` 目錄下完全缺少 `style.css` 和 `script.js` 檔案。
-   **解決方案**:
    從一個功能正常的年份（如 2024）的 `.../web/` 目錄中，將 `style.css` 和 `script.js` 複製到所有受影響年份的 `.../web/` 目錄中。

---

## 問題三：數學公式渲染失敗

這是最複雜的問題，其背後有多個環環相扣的原因。

### 階段一：診斷競爭條件 (Race Condition)

-   **症狀**: 頁面載入後，所有 LaTeX 公式都以原始碼（如 `\frac{a}{b}`）的形式顯示，而不是渲染後的數學符號。瀏覽器控制台顯示錯誤，指出 `MathJax.typesetPromise()` 不是一個函數。
-   **根本原因**: **競爭條件 (Race Condition)**。`index.html` 中用於載入外部 MathJax 庫的 `<script>` 標籤被設置為 `async`（異步）載入。這意味著瀏覽器不會等待 `MathJax.js` 完全下載並初始化，就會繼續執行後面的本地 `script.js`。因此，當 `script.js` 嘗試調用 `window.MathJax.typesetPromise()` 時，`MathJax` 物件很可能還沒有準備好，導致函數調用失敗。
-   **解決方案**: **強制同步載入**。通過移除 MathJax `<script>` 標籤中的 `async` 屬性，我們強制瀏覽器在執行後續任何 `<script>` 之前，必須先完成 `MathJax.js` 的下載和解析。

    **修改前 (錯誤的):**
    ```html
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    ```

    **修改後 (正確的):**
    ```html
    <script id="MathJax-script" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    ```

### 階段二：診斷 LaTeX 語法問題

-   **症狀**: 在解決了競爭條件後，MathJax 成功執行，但大部分公式仍然無法渲染。控制台沒有新的錯誤。
-   **根本原因**: 數據源，即對應的 `dse_m*_paper.json` 檔案中，存在大量不規範或無效的 LaTeX 語法。
    1.  **無效的命令**: 存在一些錯誤的 LaTeX 命令，例如 `\operatorname `（`\operatorname` 和後面的 `{` 之間多了一個空格）以及 `\mathbb{S}`（`\mathbb` 命令通常只對大寫字母有效，且在此上下文中無意義）。
    2.  **不標準的分隔符**: 整個專案的 JSON 檔案都使用 `$` 和 `$$` 作為 LaTeX 的行內和塊級分隔符。雖然 MathJax 可以配置支援這種用法，但這不是默認行為，且容易與貨幣符號等產生衝突。更現代、更健壯的標準是使用 `\(` 和 `\)` 作為行內分隔符，`\[` 和 `\]` 作為塊級分隔符。
-   **解決方案**: **規範化 JSON 數據**。
    1.  **修正無效命令**: 手動或通過腳本搜索並修正已知的錯誤命令（如移除 `\operatorname` 後的空格）。
    2.  **替換分隔符**: 使用正則表達式，在所有 `.json` 檔案中進行全局替換：
        -   將所有 `$$...$$` 替換為 `\\[...\\]`。
        -   將所有 `$...$` 替換為 `\\(...\\)`。

        **正則表達式示例 (用於 `search_and_replace` 工具):**
        -   **搜索 `$`**: `\$([^\$\n]+?)\$`
        -   **替換為**: `\\($1\\)`

這個徹底的數據清理最終解決了所有公式的渲染問題。

## 總結與建議

1.  **保持 HTML 結構一致性**: 確保所有試卷頁面都包含 `<main id="paper-content"></main>`。
2.  **確保檔案完整性**: 在創建新的年份頁面時，務必從模板中完整複製 `style.css` 和 `script.js`。
3.  **採用同步腳本載入**: 對於有依賴關係的腳本（如本地腳本依賴 MathJax），應避免使用 `async`，以保證正確的執行順序。
4.  **規範化數據源**: 強烈建議在生成 JSON 數據的環節就統一使用標準的 LaTeX 分隔符 `\(`...`\)` 和 `\[`...`\]`，並對 LaTeX 語法進行校驗，從源頭上避免數據污染。