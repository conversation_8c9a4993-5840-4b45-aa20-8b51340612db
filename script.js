function initializeApp() {
    const yearFilter = document.getElementById('year-filter');
    const subjectFilter = document.getElementById('subject-filter');
    const langFilter = document.getElementById('lang-filter');
    const topicFilter = document.getElementById('topic-filter');
    const keywordSearch = document.getElementById('keyword-search');
    const resultsContainer = document.getElementById('results-container');

    let allQuestions = [];
    let allTopics = new Map();
    let papers = [];
    const controls = [yearFilter, subjectFilter, langFilter, topicFilter, keywordSearch];

    // Disable controls until data is loaded
    controls.forEach(control => control.disabled = true);

    // Fetch the manifest and then all paper data
    fetch('papers_manifest.json')
        .then(response => response.json())
        .then(manifest => {
            papers = manifest;
            renderPaperCards(papers); // Initial render of paper cards

            const fetchPromises = manifest.map(paper =>
                fetch(paper.path)
                    .then(res => res.json())
                    .then(data => {
                       const paperPath = paper.path;
                       const imageBasePath = data.image_base_path ? paperPath.substring(0, paperPath.lastIndexOf('/') + 1) + data.image_base_path : paperPath.substring(0, paperPath.lastIndexOf('/') + 1) + 'images';

                        // Flatten questions and add paper info
                        data.sections.forEach(section => {
                            section.questions.forEach(question => {
                                allQuestions.push({
                                    ...question,
                                    year: data.year,
                                    lang: data.lang,
                                    paperTitle: data.title,
                                    imageBasePath: imageBasePath,
                                    subject: paper.subject
                                });
                                if (question.topic) {
                                    allTopics.set(question.topic.code, question.topic.name);
                                }
                            });
                        });
                    })
            );
            return Promise.all(fetchPromises);
        })
        .then(() => {
            populateTopics();
            controls.forEach(control => control.disabled = false); // Re-enable controls
        })
        .catch(error => {
            console.error('Error loading paper data:', error);
            resultsContainer.innerHTML = '<p>Error loading data. Please check the console.</p>';
            controls.forEach(control => control.disabled = false);
        });

   function populateTopics(subject = '') {
       topicFilter.innerHTML = '<option value="">All Topics</option>'; // Reset and add default
       let topicsToShow = new Map();

       if (subject) {
           allQuestions.forEach(q => {
               if (q.subject === subject && q.topic) {
                   topicsToShow.set(q.topic.code, q.topic.name);
               }
           });
       } else {
           // If no subject is selected, show all topics
           topicsToShow = allTopics;
       }

       const sortedTopics = new Map([...topicsToShow.entries()].sort());
       sortedTopics.forEach((name, code) => {
           const option = document.createElement('option');
           option.value = code;
           option.textContent = `${code} - ${name}`;
           topicFilter.appendChild(option);
       });
   }

    function renderPaperCards(papersToRender) {
        resultsContainer.innerHTML = '';
        resultsContainer.className = 'paper-cards'; // Use grid for paper cards
        if (papersToRender.length === 0) {
            resultsContainer.innerHTML = '<p>No papers found.</p>';
            return;
        }

        papersToRender.forEach(p => {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item paper-card';

            const title = document.createElement('h3');
            const link = document.createElement('a');
            link.textContent = `DSE ${p.subject} ${p.year} (${p.lang})`;
            link.href = p.viewer_path;
            link.target = '_blank';
            title.appendChild(link);
            
            resultItem.appendChild(title);
            resultsContainer.appendChild(resultItem);
        });
    }

    function renderResults(questions) {
        resultsContainer.innerHTML = '';
        resultsContainer.className = ''; // Use single column for question results
        if (questions.length === 0) {
            resultsContainer.innerHTML = '<p>No results found.</p>';
            return;
        }

        questions.forEach(q => {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';

            let questionHTML = `
                <div class="question-header">
                    <div>
                        <span class="question-number">Question ${q.id}</span>
                        <span class="question-meta">${q.year} | ${q.lang}</span>
                        ${q.topic ? `<span class="topic-info">${q.topic.code} | ${q.topic.name}</span>` : ''}
                    </div>
                    <span class="question-marks">(${q.marks} marks)</span>
                </div>
                <div class="question-content">
                    ${(q.content || '').replace(/\\n|\n/g, '<br>').replace(/<table>/g, '<table class="data-table">')}
                </div>
            `;

            if (q.images && q.images.length > 0) {
               q.images.forEach(imgPath => {
                   const fullImagePath = `${q.imageBasePath}/${imgPath}`;
                   questionHTML += `
                       <div class="question-image">
                           <img src="${encodeURI(fullImagePath)}" alt="Question Image">
                       </div>
                   `;
               });
            }

            if (q.sub_questions && q.sub_questions.length > 0) {
                questionHTML += renderSubQuestions(q.sub_questions, 1, q.imageBasePath);
            }
            
            resultItem.innerHTML = questionHTML;
            resultsContainer.appendChild(resultItem);
        });

        // Re-render MathJax for the new content
        if (window.MathJax) {
            window.MathJax.typesetPromise([resultsContainer]);
        }
    }

    function filterAndRender() {
        const subject = subjectFilter.value;
        const year = yearFilter.value;
        const lang = langFilter.value;
        const topic = topicFilter.value;
        const keywords = keywordSearch.value.toLowerCase();

        // If no filters are active, show paper cards. Otherwise, show question cards.
        if (!subject && !year && !lang && !topic && !keywords) {
            renderPaperCards(papers);
            return;
        }

        let filteredQuestions = allQuestions;

        if (subject) {
            filteredQuestions = filteredQuestions.filter(q => q.subject === subject);
        }

        if (year) {
            filteredQuestions = filteredQuestions.filter(q => q.year == year);
        }

        if (lang) {
            filteredQuestions = filteredQuestions.filter(q => q.lang.toLowerCase() === lang.toLowerCase());
        }

        if (topic) {
            filteredQuestions = filteredQuestions.filter(q => q.topic && q.topic.code === topic);
        }

        if (keywords) {
            filteredQuestions = filteredQuestions.filter(q => {
                const questionText = (q.content + (q.sub_questions ? JSON.stringify(q.sub_questions) : '')).toLowerCase();
                return questionText.includes(keywords);
            });
        }

        renderResults(filteredQuestions);
    }

    function renderSubQuestions(subQuestions, depth, imageBasePath) {
        let html = '<div class="sub-questions-container">';
        (subQuestions || []).forEach(sq => {
            const subQuestionContent = (sq.content || '').replace(/\\n|\n/g, '<br>').replace(/<table>/g, '<table class="data-table">');

            html += `
                <div class="sub-question depth-${depth}">
                    <div class="sub-question-header">
                        <span class="sub-question-part">(${sq.id})</span>
                        ${sq.marks ? `<span class="sub-question-marks">(${sq.marks} marks)</span>` : ''}
                    </div>
                    <div class="sub-question-content">${subQuestionContent}</div>
            `;

            if (sq.images && sq.images.length > 0) {
               sq.images.forEach(imgPath => {
                   const fullImagePath = `${imageBasePath}/${imgPath}`;
                   html += `
                       <div class="question-image">
                           <img src="${encodeURI(fullImagePath)}" alt="Sub-question Image">
                       </div>
                   `;
               });
            }

            if (sq.sub_questions && sq.sub_questions.length > 0) {
                html += renderSubQuestions(sq.sub_questions, depth + 1, imageBasePath);
            }

            html += '</div>';
        });
        html += '</div>';
        return html;
    }

   subjectFilter.addEventListener('change', () => {
       populateTopics(subjectFilter.value);
       filterAndRender();
   });
   yearFilter.addEventListener('change', filterAndRender);
   langFilter.addEventListener('change', filterAndRender);
   topicFilter.addEventListener('change', filterAndRender);
   keywordSearch.addEventListener('input', filterAndRender);
}

document.addEventListener('DOMContentLoaded', initializeApp);