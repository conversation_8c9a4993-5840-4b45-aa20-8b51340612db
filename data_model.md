# 統一數據模型 (Unified Data Model)

為了整合及標準化項目中所有試卷的數據，我們將採用以下統一的 JSON 結構。這個模型旨在解決現有數據結構不一致、資料遺失及難以擴展的問題。

## 核心原則

*   **結構化**: 使用巢狀結構清晰地表達題目、子問題、表格、圖片等不同部分的關係。
*   **關注點分離**: JSON 檔案只儲存純粹的數據，不包含任何 HTML 標籤。所有渲染邏輯由前端 JavaScript 負責。
*   **可擴展性**: 容易加入新的內容類型（例如：音訊、影片）。

## 統一 JSON 結構定義

每個問題（Question）物件將包含以下欄位：

*   `id` (string): 題號 (e.g., "1", "10a")
*   `marks` (number): 該題分數
*   `topic` (object): 題目主題
    *   `code` (string): 主題代碼 (e.g., "STA-DIS")
    *   `name` (string): 主題名稱 (e.g., "離散隨機變量")
*   `content` (string, optional): 題目的主要文字內容。
*   `parts` (array, optional): 一個陣列，用來儲存題目的各個組成部分，例如子問題、表格、圖片。

### `parts` 陣列中的物件結構

`parts` 陣列中的每個物件都必須有一個 `type` 欄位。

#### 1. 子問題 (`sub_question`)

*   `type`: "sub_question"
*   `id` (string): 子題號 (e.g., "a", "i")
*   `marks` (number, optional): 子題分數。此欄位為可選，僅當子問題有明確分數時提供。
*   `content` (string): 子題的文字內容
*   `parts` (array, optional): 子問題可以再包含自己的 `parts`，形成巢狀結構。

#### 2. 表格 (`table`)

*   `type`: "table"
*   `header` (array of strings, optional): 表格的標頭。
*   `data` (array of arrays of strings): 表格的數據內容。

#### 3. 圖片 (`image`)

*   `type`: "image"
*   `path` (string): 圖片的相對路徑。
*   `caption` (string, optional): 圖片的說明文字。

---

## 轉換範例

### 範例 1：Core 科目題目 (2022, Q9)

**原始 `full.md` 問題 (部分):**
```markdown
9. 下面的频数分布表及累積频数分布表均顯示某群學生完成某 3km 賽跑所需時間的分布。

<table><tr><td>所需時間（分鐘）</td><td>頻數</td></tr><tr><td>10 – 14</td><td>a</td></tr>...</table>
<table><tr><td>所需時間少於（分鐘）</td><td>累積頻數</td></tr><tr><td>14.5</td><td>3</td></tr>...</table>

(a) 寫出 x 的值。
...
```

**轉換後的統一 JSON 格式:**
```json
{
  "id": "9",
  "marks": 5,
  "topic": { "code": "N/A", "name": "待定" },
  "content": "下面的頻數分布表及累積頻數分布表均顯示某群學生完成某 3km 賽跑所需時間的分布。",
  "parts": [
    {
      "type": "table",
      "data": [
        ["所需時間（分鐘）", "頻數"],
        ["10 – 14", "a"],
        ["15 – 19", "9"],
        ["20 – 24", "b"],
        ["25 – 29", "3"]
      ]
    },
    {
      "type": "table",
      "data": [
        ["所需時間少於（分鐘）", "累積頻數"],
        ["14.5", "3"],
        ["19.5", "x"],
        ["24.5", "y"],
        ["29.5", "20"]
      ]
    },
    {
      "type": "sub_question",
      "id": "a",
      "content": "寫出 x 的值。"
    },
    {
      "type": "sub_question",
      "id": "b",
      "content": "求該分布的平均值。"
    },
    {
      "type": "sub_question",
      "id": "c",
      "content": "求從該群中隨機選出的一名學生完成該 3km 賽跑所需時間少於 19.5 分鐘的概率。"
    }
  ]
}
```

### 範例 2：M1 科目題目 (2020, Q1)

**原始 JSON 問題:**
```json
{
  "id": "1",
  "marks": 6,
  "topic": { "code": "STA-DIS", "name": "離散隨機變量" },
  "content": "下表顯示一離散隨機變量 \\(X\\) 的概率分怖...<table>...</table>",
  "sub_questions": [
    { "id": "a", "content": "求 \\(a\\) 。" },
    { "id": "b", "content": "若 ... ,求 \\(p\\) 。" }
  ]
}
```

**轉換後的統一 JSON 格式:**
```json
{
  "id": "1",
  "marks": 6,
  "topic": { "code": "STA-DIS", "name": "離散隨機變量" },
  "content": "下表顯示一離散隨機變量 \\(X\\) 的概率分怖，其中 \\(a\\) 為一常數及 \\(p > 0\\) 。",
  "parts": [
    {
      "type": "table",
      "data": [
        ["x", "0", "1", "2"],
        ["P(X=x)", "1-4p", "aP", "p"]
      ]
    },
    {
      "type": "sub_question",
      "id": "a",
      "content": "求 \\(a\\) 。"
    },
    {
      "type": "sub_question",
      "id": "b",
      "content": "若 \\(\\operatorname {Var}(2X + a^{2}) = 8\\operatorname {E}(a X - 1)\\) ,求 \\(p\\) 。"
    }
  ]
}