/* General Body Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f9;
    color: #333;
}

/* Container for the paper content */
.container {
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Header Styles */
header {
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
    text-align: center;
}

#paper-title {
    font-size: 2em;
    color: #333;
    margin: 0;
}

/* Section Styles */
.section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #fafafa;
}

.section-title {
    font-size: 1.5em;
    color: #0056b3;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-top: 0;
}

/* Question Styles */
.question {
    margin-bottom: 20px;
    padding: 15px;
    border-left: 4px solid #007bff;
    background-color: #fff;
    border-radius: 0 5px 5px 0;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: bold;
}

.question-id {
    font-size: 1.2em;
    color: #007bff;
}

.topic-info {
    font-size: 0.9em;
    color: #555;
    font-style: italic;
}

.question-content {
    margin-bottom: 10px;
}

.question-image {
    margin-top: 10px;
    text-align: center;
}

.question-image img {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Sub-question Styles */
.sub-questions-container {
    margin-top: 15px;
    padding-left: 20px;
    border-left: 2px solid #e0e0e0;
}

.sub-question {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.sub-question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: normal;
    color: #444;
    margin-bottom: 5px;
}

.sub-question-part {
    font-weight: bold;
}

/* Data Table Styles */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.data-table th, .data-table td {
    border: 1px solid #ccc;
    padding: 8px;
    text-align: left;
}

.data-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}