// viewer/script.js

document.addEventListener('DOMContentLoaded', function() {
    const params = new URLSearchParams(window.location.search);
    // Default to a known good paper for easy testing
    const year = params.get('year') || '2023';
    const lang = params.get('lang') || 'ENG';

    // Path is relative to `viewer/index.html`
    const jsonPath = `../M1 - latest_copy/${year}/${lang}/dse_m1_${year}_paper.json`;

    fetch(jsonPath)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status} while fetching ${jsonPath}`);
            }
            return response.json();
        })
        .then(data => {
            const container = document.getElementById('paper-container');
            document.title = data.title || `DSE M1 ${year} Paper`;
            renderPaper(data, container, year, lang);
            
            // Ensure MathJax is ready before typesetting
            if (window.MathJax && window.MathJax.startup) {
                window.MathJax.startup.promise.then(() => {
                    window.MathJax.typesetPromise([container]);
                });
            }
        })
        .catch(error => {
            console.error('Error fetching or parsing paper data:', error);
            const container = document.getElementById('paper-container');
            container.innerHTML = `<p style="color: red;">Could not load paper data. Please check the URL parameters (e.g., ?year=2023&lang=ENG) and see the console for errors.</p><p><b>Error:</b> ${error.message}</p>`;
        });
});

function renderPaper(data, container, year, lang) {
    // Using the new, flatter JSON structure (from 2023 files)
    const { title, image_base_path, sections } = data;

    let html = `
        <div class="header">
            <h1>${title}</h1>
            <p>${year} &ndash; ${lang}</p>
        </div>
    `;

    if (!sections) {
        container.innerHTML = '<p style="color: red;">JSON data is missing the "sections" array.</p>';
        return;
    }

    sections.forEach(section => {
        html += `
            <div class="section">
                <div class="section-header">
                    ${section.title} (Marks: ${section.total_marks})
                </div>
        `;
        (section.questions || []).forEach(q => {
            // Add class to tables for styling
            const questionContent = (q.content || '').replace(/<table>/g, '<table class="data-table">');

            html += `
                <div class="question">
                    <div class="question-header">
                        <div>
                            <span class="question-number">Question ${q.id}</span>
                            ${q.topic ? `<span class="topic-info">${q.topic.code} | ${q.topic.name}</span>` : ''}
                        </div>
                        <span class="question-marks">(${q.marks} marks)</span>
                    </div>
                    <div class="question-content">
                        ${questionContent}
                    </div>
            `;

            if (q.images && q.images.length > 0) {
               const imagePathPrefix = `../M1 - latest_copy/${year}/${lang}/${image_base_path}/`;
               q.images.forEach(imgPath => {
                   const fullImagePath = `${imagePathPrefix}${imgPath}`;
                   html += `
                       <div class="question-image">
                           <img src="${fullImagePath}" alt="Question Image">
                       </div>
                   `;
               });
            }

            if (q.sub_questions && q.sub_questions.length > 0) {
                html += renderSubQuestions(q.sub_questions, 1, image_base_path, year, lang);
            }

            html += '</div>'; // close question
        });
        html += '</div>'; // close section
    });

    container.innerHTML = html;
}

function renderSubQuestions(subQuestions, depth, image_base_path, year, lang) {
    let html = '<div class="sub-questions-container">';
    (subQuestions || []).forEach(sq => {
        // Add class to tables for styling
        const subQuestionContent = (sq.content || '').replace(/<table>/g, '<table class="data-table">');

        html += `
            <div class="sub-question depth-${depth}">
                <div class="sub-question-header">
                    <span class="sub-question-part">(${sq.id})</span>
                    ${sq.marks ? `<span class="sub-question-marks">(${sq.marks} marks)</span>` : ''}
                </div>
                <div class="sub-question-content">${subQuestionContent}</div>
        `;

        if (sq.images && sq.images.length > 0) {
           const imagePathPrefix = `../M1 - latest_copy/${year}/${lang}/${image_base_path}/`;
           sq.images.forEach(imgPath => {
               const fullImagePath = `${imagePathPrefix}${imgPath}`;
               html += `
                   <div class="question-image">
                       <img src="${fullImagePath}" alt="Sub-question Image">
                   </div>
               `;
           });
        }

        // Recursive call for nested sub-questions
        if (sq.sub_questions && sq.sub_questions.length > 0) {
            html += renderSubQuestions(sq.sub_questions, depth + 1, image_base_path, year, lang);
        }

        html += '</div>'; // close sub-question
    });
    html += '</div>'; // close sub-questions-container
    return html;
}