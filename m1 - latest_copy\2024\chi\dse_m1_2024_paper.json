{"year": 2024, "lang": "CHI", "subject": "M1", "title": "香港考試及評核局 香港中學文憑考試 2024 數學 延伸部分 單元一 (微積分與統計)", "image_base_path": "2024-sample-math-m1-level1-c-976d.pdf-ef5ae32a-9049-4596-91b1-3e199c716d9b", "sections": [{"id": "A", "title": "甲部", "total_marks": 50, "questions": [{"id": "1", "marks": 7, "topic": {"code": "STA-DIS", "name": "離散隨機變數"}, "content": "下表顯示一離散隨機變量  \\(X\\)  的概率分怖,其中  \\(a\\)  及  \\(b\\)  均為常數使得  \\(6< b< 15\\)  。\\n\\n<table><tr><td>x</td><td>0</td><td>3</td><td>6</td><td>b</td><td>15</td></tr><tr><td>P(X=x)</td><td>0.3</td><td>a</td><td>0.1</td><td>0.2</td><td>0.2</td></tr></table>\\n\\n已知  \\(\\operatorname {Var}(5X) = 739\\)  。", "sub_questions": [{"id": "a", "content": "求  \\(a\\)  及  \\(b\\)  。"}, {"id": "b", "content": "設  \\(c\\)  為事件  \\(0< X\\leq 7\\)  。\\n(i) 設  \\(D\\)  為事件  \\(4< X\\leq 15\\)  。  \\(c\\)  與  \\(D\\)  是否獨立?解釋你的答案。\\n(ii)設  \\(E\\)  為一事件使得  \\(\\mathbb{P}(E)\\neq 0\\)  。若  \\(c\\)  與  \\(E\\)  互斥,寫出  \\(\\mathbb{P}(E)\\)  的最大可取值。"}], "images": []}, {"id": "2", "marks": 6, "topic": {"code": "STA-PROB", "name": "條件概率"}, "content": "在一樂團中  \\(\\frac{3}{5}\\)  的成員佩戴眼鏡。在男成員當中,有  \\(\\frac{4}{9}\\)  佩戴眼鏡。隨機選出的一名成員是沒有佩戴眼鏡的女性的概率為  \\(\\frac{3}{20}\\) 。", "sub_questions": [{"id": "a", "content": "已知隨機選出的一名成員沒有佩戴眼鏡,求該成員是女性的概率。"}, {"id": "b", "content": "求隨機選出的一名成員是佩戴眼鏡的女性的概率。"}], "images": []}, {"id": "3", "marks": 6, "topic": {"code": "STA-BIN", "name": "二項分佈"}, "content": "投擲一枚硬幣,擲得字的概率為  \\(p\\) ,其中  \\(0 < p < 1\\) 。當投擲該硬幣 20 次時,擲得 1 次字的概率與擲得 3 次字的概率之比為 49:57。", "sub_questions": [{"id": "a", "content": "求  \\(p\\) 。"}, {"id": "b", "content": "投擲該硬幣  \\(k\\)  求。求  \\(k\\)  的最小值使擲得至少 1 次字的概率大於 0.85。"}], "images": []}, {"id": "4", "marks": 6, "topic": {"code": "STA-NORM", "name": "正態分佈"}, "content": "某校各學生每星期的溫習時間依循一平均值為  \\(\\mu\\)  小時的正態分布。從該校隨機選取一個 81 名學生的樣本。這些學生每星期的溫習時間之平均值及標準差分別為 13 小時及 1.75 小時。", "sub_questions": [{"id": "a", "content": "\\(\\mu\\)  的  \\(\\beta %\\)  置信區間的寬度為 0.7。求  \\(\\beta\\) 。"}, {"id": "b", "content": "已知該樣本中有 36 名男生,且這些男生每星期的溫習時間之平均值及標準差分別為 12.5 小時及 2 小時。求該樣本中女生每星期的溫習時間之標準差。\\n\\n提示:樣本標準差為  \\(\\sqrt{\\frac{1}{n - 1}\\left(\\sum_{i = 1}^{n}x_{i}^{2} - n\\vec{x}^{2}\\right)} \\circ\\)"}], "images": []}, {"id": "5", "marks": 7, "topic": {"code": "CAL-EXP", "name": "指數級數"}, "content": "設  \\(n\\)  為一正數。", "sub_questions": [{"id": "a", "content": "依  \\(x\\)  的升幂次序展開  \\(\\frac{2}{e^{nx}}\\)  至含  \\(x^{3}\\)  的填為止。"}, {"id": "b", "content": "考慮  \\((1 + 4x)^{m} + \\frac{2}{e^{nx}}\\)  的展開式,其中  \\(m\\)  為一正整數。已知該展開式中  \\(x\\)  及  \\(x^{2}\\)  的係數分別為 24 及 980。求該展開式中  \\(x^{3}\\)  的係數。"}], "images": []}, {"id": "6", "marks": 7, "topic": {"code": "CAL-APP", "name": "微分法的應用"}, "content": "", "sub_questions": [{"id": "a", "content": "设  \\(e^{u} = (x^{2} + x + e)^{2x + 1}\\)  。\\n(i) 将  \\(u\\)  表為  \\(\\mathbf{p}(x)\\ln (\\mathbf{q}(x))\\)  的形式,其中  \\(\\mathbf{p}(x)\\)  及  \\(\\mathbf{q}(x)\\)  均為多項式。\\n(ii) 以  \\(x\\)  表  \\(\\frac{\\mathrm{d}}{\\mathrm{d}x} e^{u}\\)  。"}, {"id": "b", "content": "曲線  \\(\\boldsymbol{\\Gamma}\\)  的方程為  \\(y = (x^{2} + x + e)^{2x + 1}\\)  。將  \\(\\boldsymbol{\\Gamma}\\)  與  \\(y\\)  輸的交點記為  \\(H\\)  。求  \\(\\boldsymbol{\\Gamma}\\)  在  \\(H\\)  的切線的方程。"}], "images": []}, {"id": "7", "marks": 4, "topic": {"code": "CAL-APP", "name": "微分法的應用"}, "content": "某電腦程式調整一長方形數碼圖片的長及，使得其對角線的長度維持不變，而其以低速率  \\(0.5\\mathrm{cm}\\mathrm{s}^{-1}\\)  减少。初始時，該圖片的長及分别為  \\(20~\\mathrm{cm}\\)  及  \\(15~\\mathrm{cm}\\)  。把該圖片的記為  \\(x\\mathrm{cm}\\)  。求當  \\(x = 7\\)  時該圖片的面的變率。", "images": []}, {"id": "8", "marks": 7, "topic": {"code": "CAL-INT", "name": "定積分"}, "content": "", "sub_questions": [{"id": "a", "content": "利用第24頁的標準正懸分佈表,計算  \\(\\int_{0}^{0.5} e^{-\\frac{x^{2}}{2}} \\mathrm{~d} x\\)  。"}, {"id": "b", "content": "考慮曲線  \\(C: y = (2x - 1) e^{-\\frac{x^{2}}{2}}\\) ,其中  \\(x \\geq 0\\)  。利用(a) 的結果,求  \\(C \\cdot x\\)  軸與  \\(y\\)  軸圍成的區域的面積。"}], "images": []}]}, {"id": "B", "title": "乙部", "total_marks": 50, "questions": [{"id": "9", "marks": 11, "topic": {"code": "STA-NORM", "name": "正態分佈"}, "content": "某大市場內每個南瓜的重量依循一平均值為  \\(\\mu \\mathrm{kg}\\)  及標準差為  \\(\\sigma \\mathrm{kg}\\)  的正態分布。已知該市場內  \\(30.85%\\)  的南瓜每個重量均多於  \\(5.7 \\mathrm{kg}\\)  ，而  \\(78.88%\\)  的南瓜每個重量均介乎  \\((\\mu -1.5) \\mathrm{kg}\\) 與  \\((\\mu +1.5) \\mathrm{kg}\\)  之間。", "sub_questions": [{"id": "a", "content": "求  \\(\\mu\\)  及  \\(\\sigma\\)  。 (3 分) "}, {"id": "b", "content": "假定在該市場內隨機選取16 個南瓜。求這些南瓜的平均重量不超過  \\(5.4 \\mathrm{kg}\\)  的概率。(2 分)"}, {"id": "c", "content": "下表顯示該市場內的南瓜的等級及價錢。\\n\\n<table><tr><td>一個南瓜的重量（Wkg）</td><td>W≤3.6</td><td>3.6＜W≤5.7</td><td>W＞5.7</td></tr><tr><td>等級</td><td>C</td><td>B</td><td>A</td></tr><tr><td>價錢 ($)</td><td>50</td><td>80</td><td>100</td></tr></table>\\n\\n假定在該市場內機選取8個南瓜並把這些南瓜放進一手推車內。\\n(i) 求手推車内該台南瓜價錢的期望值。\\n(ii) 求手推車内有至少5個B等级的南瓜及至少1個A等级的南瓜的概率。\\n\\n(6 分)"}], "images": []}, {"id": "10", "marks": 12, "topic": {"code": "STA-POI", "name": "泊松分佈"}, "content": "一派遞員每天均派送貨物。每天派送延誤的數目依循一平均值為 1.6 的泊松分佈。若某天延誤的數目少於 3,則當天稱為順利。", "sub_questions": [{"id": "a", "content": "求某天為順利的概率。 (2分)"}, {"id": "b", "content": "求某星期内全部 7 天均為順利的概率。 (2分)"}, {"id": "c", "content": "已知某星期内全部 7 天均為順利,求該星期内恰有 10 次延誤的概率。 (4分)"}, {"id": "d", "content": "已知某星期内有至少 2 天沒有延誤,求該星期内全部 7 天均為順利的概率。 (4分)"}], "images": []}, {"id": "11", "marks": 14, "topic": {"code": "CAL-EXP", "name": "指數及對數函數"}, "content": "某天 \\(\\mathbf{M}\\) 城的累積雨量以每小時 \\(P\\mathrm{mm}\\) 的速率增加。已知\\[P = a(-t^{2} + 10t + 8)e^{bt},\\]其中 \\(a\\) 及 \\(b\\) 均為常數,且 \\(t\\) (\\(0 \\leq t \\leq 4\\))為自當天上午7時起計所經過的時數。得知 \\(\\ln \\left(\\frac{P}{- t^{2} + 10t + 8}\\right)\\) 為 \\(t\\) 的線性函数,且這線性函数的圖像通過點 \\((3, - 0.1)\\) 及水平軸上的截距為2.5。", "sub_questions": [{"id": "a", "content": "將 \\(\\ln \\left(\\frac{P}{-t^{2} + 10t + 8}\\right)\\) 表為 \\(t\\) 的線性函数。 (1分)"}, {"id": "b", "content": "求 \\(a\\) 及 \\(b\\) 的真確值。 (3分)"}, {"id": "c", "content": "利用梯形法則將區間分成4個子區間,估計 \\(\\mathbf{M}\\) 城當天由上午7時至上午11時的累積雨量。 (2分)"}, {"id": "d", "content": "同一天 \\(\\mathbf{N}\\) 城的累積雨量以每小時 \\(Q\\mathrm{mm}\\) 的速率增加。已知\\[Q = \\frac{16(2t + 5)e^{0.4t}}{4t e^{0.4t} + 3},\\]其中 \\(t\\) (\\(0 \\leq t \\leq 4\\))為自當天上午7時起計所經過的時數。\\n\\n(i) 求 \\(\\int Q \\mathrm{~d}t\\) 。\\n(ii) 某人宣稱 \\(\\mathbf{M}\\) 城與 \\(\\mathbf{N}\\) 城當天由上午7時至上午11時的累積雨量之和大於 \\(160 \\mathrm{~mm}\\) 。你是否同意?解釋你的答案。\\n\\n(8分)"}], "images": []}, {"id": "12", "marks": 13, "topic": {"code": "CAL-TECH", "name": "積分技巧"}, "content": "段 \\(R\\) (以千元為單位)為某網上商店的總營業額。已知\\[\\frac{\\mathrm{d}R}{\\mathrm{d}t} = \\frac{2e^{0.5t} - 5e^{-0.5t}}{2e^{0.5t} + 5e^{-0.5t} - 5} +2,\\]其中 \\(t(t \\geq 0)\\) 為自該商店開張起計所經過的月數。", "sub_questions": [{"id": "a", "content": "該商店的總營業額的最大變率是否超過每月 4 千元？解釋你的答案。 (4分)"}, {"id": "b", "content": "設 \\(P\\) (以千元為單位)為該商店的總盈利。已知\\[\\frac{\\mathrm{d}P}{\\mathrm{d}t} = \\frac{\\mathrm{d}R}{\\mathrm{d}t} -10(0.8)^{2t + 3},\\]其中 \\(t(t \\geq 0)\\) 為自該商店開張起計所經過的月數。\\n\\n(i) 求自該商店開張起計首 12 個月內該商店的總盈利。\\n(ii) 估計經過一段很長時間後該商店的總盈利的變率。\\n\\n(9分)"}], "images": []}]}], "images": []}