# 專案改進計畫

## 1. 總覽

最近的錯誤修復過程凸顯了 DSE 試卷檢視器專案中的幾個架構弱點。雖然眼前的問題已經解決，但專案的結構很脆弱且難以維護。核心問題是文件備援和分散式結構，這導致了不一致和最初的錯誤。

本文件概述了一個重構專案的計畫，以提高可維護性、可擴展性和穩健性。

## 2. 當前架構的主要問題

*   **文件重複**：每個考試試卷都有自己的 `web` 目錄，其中包含相同的 `index.html`、`style.css` 和 `script.js` 副本。這使得更新變得繁瑣且容易出錯。
*   **分散式結構**：應用程式沒有單一進入點。要查看試卷，必須導覽至其特定的 `index.html` 檔案。
*   **脆弱的依賴關係**：與 MathJax 的競爭條件表明，對外部函式庫的處理不夠穩健。

## 3. 建議的架構變更

### 3.1. 集中式 `web` 目錄

最關鍵的變更是消除多餘的 `web` 目錄。

*   **操作**：建立一個單一的、頂層的 `web` 目錄（例如 `/web`）。
*   **內容**：此目錄將包含一個 `index.html`、一個 `style.css` 和一個 `script.js`。
*   **優點**：對網站範本、樣式或核心邏輯的任何變更都只需要在一個地方進行，確保所有試卷的一致性。

### 3.2. 透過 URL 參數動態載入內容

單一的 `index.html` 將成為一個動態載入試卷資料的範本。

*   **操作**：修改 `script.js` 以解析 URL 查詢參數（例如 `?year=2023&lang=eng&subject=M1`）。
*   **邏輯**：
    1.  腳本將從 URL 中讀取 `year`、`lang` 和 `subject`。
    2.  它將使用此資訊建構對應 JSON 檔案的路徑（例如 `../m1 - latest_copy/2023/eng/dse_m1_2023_paper.json`）。
    3.  然後它將 `fetch` 並呈現該 JSON 檔案的內容。
*   **優點**：這創造了單頁應用程式（SPA）的感覺。新增新試卷變得像新增一個新的 JSON 檔案一樣簡單；不需要新的 HTML 檔案。

### 3.3. 穩健的非同步腳本處理

雖然強制同步載入 MathJax 是可行的，但更現代化和高效能的解決方案是使用 Promises。

*   **操作**：重構 `script.js` 以正確處理 MathJax 的非同步特性。
*   **邏輯**：
    ```javascript
    // script.js

    // 確保在使用 MathJax 之前已載入
    window.MathJax = {
      startup: {
        ready: () => {
          console.log('MathJax 已準備就緒！');
          MathJax.startup.defaultReady();
          // 現在 MathJax 已準備就緒，我們可以安全地擷取和呈現我們的內容
          loadPaperContent();
        }
      }
    };

    function loadPaperContent() {
      // ... 現有邏輯以擷取 JSON 並填入 #paper-content ...
      // 這部分現在只會在 MathJax 確認準備就緒後執行

      // 填入內容後：
      MathJax.typesetPromise();
    }

    // MathJax 腳本標籤可以保持非同步
    // <script id="MathJax-script" async src="..."></script>
    ```
*   **優點**：這是一個更穩健的模式，可以避免阻塞頁面呈現，並正確處理本地腳本和外部函式庫之間的依賴關係。

## 4. 實施步驟

1.  **建立頂層 `web` 目錄。**
2.  **將更正後的試卷（例如 2023 M1 英文）中的 `index.html`、`style.css` 和 `script.js` 複製到新的 `web` 目錄中。**
3.  **修改新的 `script.js` 以實施 URL 參數解析和動態 JSON 路徑建構。**
4.  **重構 `script.js` 以使用基於 Promise 的方法來載入 MathJax。**
5.  **刪除所有舊的、個別的 `web` 目錄。**
6.  **更新任何進入點或連結，以指向具有適當 URL 參數的新的集中式 `index.html`。**

透過實施這些變更，專案將變得更加專業、易於管理，並且不易出現剛剛修復的那些類型的錯誤。
